import { useState, useCallback } from 'react';

export interface StreamMessage {
  type: 'thinking' | 'saving' | 'done' | 'error';
  content?: string;
  slug?: string;
}

export const useAnalysisStream = () => {
  const [isStreaming, setIsStreaming] = useState(false);
  const [thinkingText, setThinkingText] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [finalSlug, setFinalSlug] = useState<string | null>(null);

  const startStream = useCallback(async (analysisId: string, youtubeUrl: string) => {
    if (isStreaming) return;

    setIsStreaming(true);
    setThinkingText('');
    setError(null);
    setFinalSlug(null);

    try {
      const response = await fetch(`/api/analyses/${analysisId}/trigger-vertex-analysis`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ youtubeUrl }),
      });

      if (!response.ok || !response.body) {
        const err = await response.json().catch(() => ({ error: 'Failed to start analysis stream' }));
        throw new Error(err.error);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      const processStream = async () => {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            setIsStreaming(false);
            break;
          }

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n\n').filter(line => line.trim() !== '');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const jsonString = line.substring(6);
              try {
                const message: StreamMessage = JSON.parse(jsonString);
                switch (message.type) {
                  case 'thinking':
                    setThinkingText(prev => prev + message.content);
                    break;
                  case 'saving':
                    setThinkingText('Saving final analysis...');
                    break;
                  case 'done':
                    setFinalSlug(message.slug || null);
                    setIsStreaming(false);
                    break;
                  case 'error':
                    setError(message.content || 'An unknown stream error occurred.');
                    setIsStreaming(false);
                    break;
                }
              } catch (e) {
                console.error('Failed to parse stream message:', jsonString);
              }
            }
          }
        }
      };
      processStream();
    } catch (err: any) {
      setError(err.message);
      setIsStreaming(false);
    }
  }, [isStreaming]);

  return { isStreaming, thinkingText, error, finalSlug, startStream };
};