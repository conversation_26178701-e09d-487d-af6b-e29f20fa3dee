'use client'

import { useState } from 'react'
import Image from 'next/image'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
// Using custom alert styling instead of Alert component
import { Clock, Send, AlertTriangle } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'

interface AdminSubmissionFormProps {
  videoData: {
    url: string
    title: string
    channelTitle: string
    duration: string
    thumbnailUrl: string
  }
  onClose: () => void
  onSubmitted: () => void
}

export default function AdminSubmissionForm({ videoData, onClose, onSubmitted }: AdminSubmissionFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [reason, setReason] = useState('')
  const { user } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!reason.trim()) {
      setError('Please provide a reason for your request')
      return
    }

    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/admin-submissions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          youtubeUrl: videoData.url,
          videoTitle: videoData.title,
          channelTitle: videoData.channelTitle,
          duration: videoData.duration,
          reason: reason.trim(),
          userEmail: user?.emailAddresses?.[0]?.emailAddress || '<EMAIL>'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to submit request')
      }

      setSuccess(true)
      setTimeout(() => {
        onSubmitted()
        onClose()
      }, 2000)

    } catch (err: any) {
      setError(err.message || 'Failed to submit request')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="text-green-600">Request Submitted Successfully!</CardTitle>
          <CardDescription>
            Your request has been submitted to our admin team for review.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-start gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <Send className="h-4 w-4 text-green-600 dark:text-green-400 mt-0.5" />
            <p className="text-sm text-green-800 dark:text-green-200">
              We&apos;ll review your request and potentially add this video to our public analysis library. 
              You&apos;ll be notified via email once the analysis is complete.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5 text-orange-500" />
          Request Analysis for Long Video
        </CardTitle>
        <CardDescription>
          This video exceeds our 3-minute analysis limit. Submit a request for our admin team to analyze it manually.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Video Preview */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex gap-4">
            <Image 
              src={videoData.thumbnailUrl} 
              alt={videoData.title}
              width={128}
              height={80}
              className="w-32 h-20 object-cover rounded"
            />
            <div className="flex-1">
              <h3 className="font-semibold text-sm mb-1">{videoData.title}</h3>
              <p className="text-xs text-gray-600 dark:text-gray-400 mb-1">{videoData.channelTitle}</p>
              <p className="text-xs text-orange-600 font-medium">Duration: {videoData.duration}</p>
            </div>
          </div>
        </div>

        {/* Warning Alert */}
        <div className="flex items-start gap-3 p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
          <AlertTriangle className="h-4 w-4 text-orange-600 dark:text-orange-400 mt-0.5" />
          <p className="text-sm text-orange-800 dark:text-orange-200">
            <strong>Note:</strong> Admin-analyzed videos may be added to our public analysis library 
            for other users to benefit from. Personal or sensitive content should not be submitted.
          </p>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="reason">Reason for Analysis Request *</Label>
            <Textarea
              id="reason"
              placeholder="Why is this video important to analyze? (e.g., major brand campaign, innovative ad format, industry significance)"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="mt-2"
              rows={4}
              required
            />
          </div>

          {error && (
            <div className="flex items-start gap-3 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
              <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400 mt-0.5" />
              <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Submitting...' : 'Submit Request'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}