'use client'

import { useState } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Crown, Sparkles } from 'lucide-react'
import Link from 'next/link'

interface CreditExhaustionModalProps {
  isOpen: boolean
  onClose: () => void
  creditsRemaining: number
  creditsRequired: number
}

export default function CreditExhaustionModal({ 
  isOpen, 
  onClose, 
  creditsRemaining, 
  creditsRequired 
}: CreditExhaustionModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-2 mb-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            <DialogTitle>Insufficient Credits</DialogTitle>
          </div>
          <DialogDescription className="text-left">
            You need <strong>{creditsRequired} credits</strong> to analyze this video, but you only have{' '}
            <strong>{creditsRemaining} credits</strong> remaining.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Sparkles className="h-4 w-4 text-blue-600" />
              <p className="text-sm font-medium text-blue-900">Need more credits?</p>
            </div>
            <p className="text-sm text-blue-700">
              Upgrade to a Pro plan to get unlimited analyses and advanced features.
            </p>
          </div>

          <div className="flex flex-col gap-2">
            <Link href="/billing">
              <Button className="w-full" size="sm">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade to Pro
              </Button>
            </Link>
            <Button variant="outline" onClick={onClose} size="sm">
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}