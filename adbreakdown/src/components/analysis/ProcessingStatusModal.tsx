
'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface ProcessingStatusModalProps {
  isOpen: boolean;
  thinkingText: string;
}

const ProcessingStatusModal = ({ isOpen, thinkingText }: ProcessingStatusModalProps) => {
  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 z-50 flex items-center justify-center p-4">
      <style jsx global>{`
        @keyframes pulse-horizontal {
          0% { transform: translateX(-100%); }
          50% { transform: translateX(100%); }
          100% { transform: translateX(-100%); }
        }
        .animate-pulse-horizontal {
          animation: pulse-horizontal 2s ease-in-out infinite;
        }
      `}</style>
      <Card className="w-full max-w-lg">
        <CardHeader>
          <CardTitle className="flex items-center justify-center gap-2">
            <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
            Analysis in Progress
          </CardTitle>
          <CardDescription>The AI is thinking. This can take a minute or two.</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4 overflow-hidden">
            <div className="bg-blue-600 h-2.5 rounded-full animate-pulse-horizontal w-1/3"></div>
          </div>
          <div className="bg-gray-900 text-white font-mono text-xs p-4 rounded-md max-h-60 overflow-y-auto">
            <pre className="whitespace-pre-wrap">
              {thinkingText || "Initializing analysis stream..."}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default ProcessingStatusModal
