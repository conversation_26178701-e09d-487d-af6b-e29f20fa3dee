// YouTube URL validation with duration and public analysis checking
import { createServerSupabaseClient } from '@/lib/supabase'
import { parseYouTubeUrl } from '@/lib/slug-utils'

export interface YouTubeValidationResult {
  isValid: boolean
  error?: string
  videoId?: string
  normalizedUrl?: string
  duration?: {
    seconds: number
    formatted: string
  }
  existingPublicAnalysis?: {
    id: string
    slug: string
    title: string
    inferred_brand: string
  }
  metadata?: {
    title: string
    channelTitle: string
    description: string
    publishedAt: string
    thumbnailUrl: string
  }
}

export interface YouTubeAPIResponse {
  items: Array<{
    id: string
    snippet: {
      title: string
      description: string
      channelTitle: string
      publishedAt: string
      thumbnails: {
        high: { url: string }
        medium: { url: string }
        default: { url: string }
      }
      categoryId: string
      tags?: string[]
    }
    contentDetails: {
      duration: string // ISO 8601 format like PT1M30S
    }
    statistics: {
      viewCount: string
      likeCount: string
      commentCount: string
    }
  }>
}

// Convert ISO 8601 duration (PT1M30S) to seconds
export function parseDurationToSeconds(duration: string): number {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
  if (!match) return 0
  
  const hours = parseInt(match[1] || '0')
  const minutes = parseInt(match[2] || '0')
  const seconds = parseInt(match[3] || '0')
  
  return hours * 3600 + minutes * 60 + seconds
}

// Convert seconds to MM:SS or H:MM:SS format
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`
}

// Check if video appears to be an advertisement based on metadata
export function isLikelyAdvertisement(metadata: YouTubeAPIResponse['items'][0]): boolean {
  const { snippet } = metadata
  const title = snippet.title.toLowerCase()
  const description = snippet.description.toLowerCase()
  const channelTitle = snippet.channelTitle.toLowerCase()
  
  // Keywords that suggest it's an ad
  const adKeywords = [
    'ad', 'advertisement', 'commercial', 'promo', 'promotion',
    'sponsored', 'marketing', 'campaign', 'brand', 'product',
    'launch', 'announce', 'introducing', 'new', 'buy now',
    'limited time', 'sale', 'discount', 'offer', 'deal'
  ]
  
  // Channel patterns that suggest official brand channels
  const brandPatterns = [
    /official$/i,
    /inc$/i,
    /ltd$/i,
    /corp$/i,
    /company$/i,
    /brand$/i
  ]
  
  // Check title for ad keywords
  const titleHasAdKeywords = adKeywords.some(keyword => 
    title.includes(keyword)
  )
  
  // Check description for ad keywords
  const descriptionHasAdKeywords = adKeywords.some(keyword => 
    description.includes(keyword)
  )
  
  // Check if channel name suggests it's a brand
  const isLikelyBrandChannel = brandPatterns.some(pattern => 
    pattern.test(channelTitle)
  )
  
  // Video is likely an ad if:
  // 1. Title contains ad keywords, OR
  // 2. Description contains ad keywords AND channel seems like a brand
  return titleHasAdKeywords || (descriptionHasAdKeywords && isLikelyBrandChannel)
}

// Comprehensive YouTube URL validation
export async function validateYouTubeUrl(
  url: string,
  maxDurationSeconds: number = 180 // 3 minutes default
): Promise<YouTubeValidationResult> {
  try {
    // Step 1: Basic URL validation
    const urlInfo = parseYouTubeUrl(url)
    if (!urlInfo) {
      return {
        isValid: false,
        error: 'Invalid YouTube URL format'
      }
    }

    // Step 2: Check for existing public analysis
    const supabase = createServerSupabaseClient()
    const { data: publicAnalysis, error: publicError } = await supabase
      .from('ad_analyses')
      .select('id, slug, title, inferred_brand')
      .eq('youtube_video_id', urlInfo.videoId)
      .eq('is_public', true)
      .eq('status', 'completed')
      .single()

    // Step 3: Fetch YouTube metadata
    const ytApiKey = process.env.YOUTUBE_API_KEY || process.env.NEXT_PUBLIC_YOUTUBE_API_KEY
    if (!ytApiKey) {
      return {
        isValid: false,
        error: 'YouTube API key not configured'
      }
    }

    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?id=${urlInfo.videoId}&part=snippet,contentDetails,statistics&key=${ytApiKey}`
    )

    if (!response.ok) {
      return {
        isValid: false,
        error: 'Failed to fetch video information from YouTube'
      }
    }

    const data: YouTubeAPIResponse = await response.json()
    
    if (!data.items || data.items.length === 0) {
      return {
        isValid: false,
        error: 'Video not found or is private/deleted'
      }
    }

    const videoData = data.items[0]
    
    // Step 4: Parse and validate duration
    const durationSeconds = parseDurationToSeconds(videoData.contentDetails.duration)
    const durationFormatted = formatDuration(durationSeconds)
    
    if (durationSeconds > maxDurationSeconds) {
      return {
        isValid: false,
        error: `Video duration (${durationFormatted}) exceeds the ${formatDuration(maxDurationSeconds)} limit`,
        videoId: urlInfo.videoId,
        normalizedUrl: urlInfo.normalizedUrl,
        duration: {
          seconds: durationSeconds,
          formatted: durationFormatted
        },
        metadata: {
          title: videoData.snippet.title,
          channelTitle: videoData.snippet.channelTitle,
          description: videoData.snippet.description,
          publishedAt: videoData.snippet.publishedAt,
          thumbnailUrl: videoData.snippet.thumbnails.high?.url || videoData.snippet.thumbnails.medium?.url || videoData.snippet.thumbnails.default?.url
        }
      }
    }

    // Step 5: Check if it's likely an advertisement (optional warning)
    const isLikelyAd = isLikelyAdvertisement(videoData)
    
    if (!isLikelyAd) {
      console.warn('⚠️ Video may not be an advertisement based on metadata analysis')
    }

    // Step 6: Return successful validation result
    return {
      isValid: true,
      videoId: urlInfo.videoId,
      normalizedUrl: urlInfo.normalizedUrl,
      duration: {
        seconds: durationSeconds,
        formatted: durationFormatted
      },
      existingPublicAnalysis: publicAnalysis && !publicError ? {
        id: publicAnalysis.id,
        slug: publicAnalysis.slug,
        title: publicAnalysis.title,
        inferred_brand: publicAnalysis.inferred_brand
      } : undefined,
      metadata: {
        title: videoData.snippet.title,
        channelTitle: videoData.snippet.channelTitle,
        description: videoData.snippet.description,
        publishedAt: videoData.snippet.publishedAt,
        thumbnailUrl: videoData.snippet.thumbnails.high?.url || videoData.snippet.thumbnails.medium?.url || videoData.snippet.thumbnails.default?.url
      }
    }

  } catch (error) {
    console.error('Error validating YouTube URL:', error)
    return {
      isValid: false,
      error: 'Failed to validate YouTube URL'
    }
  }
}

// Admin submission interface
export interface AdminSubmissionData {
  youtubeUrl: string
  userEmail: string
  videoTitle: string
  channelTitle: string
  duration: string
  requestedAt: string
  reason: string
}

// Store admin submission request
export async function submitToAdmin(data: AdminSubmissionData): Promise<boolean> {
  try {
    const supabase = createServerSupabaseClient()
    
    // Create admin_submissions table if it doesn't exist (handled by migration)
    const { error } = await supabase
      .from('admin_submissions')
      .insert({
        youtube_url: data.youtubeUrl,
        user_email: data.userEmail,
        video_title: data.videoTitle,
        channel_title: data.channelTitle,
        duration: data.duration,
        reason: data.reason,
        status: 'pending',
        created_at: new Date().toISOString()
      })

    if (error) {
      console.error('Error submitting to admin:', error)
      return false
    }

    return true
  } catch (error) {
    console.error('Error in submitToAdmin:', error)
    return false
  }
}