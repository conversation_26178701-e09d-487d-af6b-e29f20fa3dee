// Alternative approach using environment variables
export const getMarketingAnalysisPromptFromEnv = (videoUrl: string): string => {
  const customPrompt = process.env.MARKETING_ANALYSIS_PROMPT
  
  if (customPrompt) {
    return customPrompt.replace('{{videoUrl}}', videoUrl)
  }
  
  // Fallback to original prompt
  const { getMarketingAnalysisPrompt } = require('./marketingAnalysisPrompt')
  return getMarketingAnalysisPrompt(videoUrl)
}