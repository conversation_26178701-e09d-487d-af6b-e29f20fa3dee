/**
 * Gemini API Schema for Marketing Analysis Response
 * 
 * This schema enforces the structure of the marketing analysis response
 * from the Gemini API using Gemini's expected schema format.
 */

export const marketingAnalysisSchema = {
  type: "OBJECT",
  properties: {
    metadata: {
      type: "OBJECT",
      properties: {
        ad_title: { type: "STRING" },
        brand: { type: "STRING" },
        product_category: { type: "STRING" },
        parent_entity: { type: "STRING" },
        campaign_category: { type: "STRING" },
        runtime: { type: "STRING" },
        celebrity: { type: "STRING" },
        creative_agency: { type: "STRING" },
        geography: { type: "STRING" },
        agency: { type: "STRING" },
        director: { type: "STRING" },
        creative_team: { type: "STRING" },
        production_company: { type: "STRING" },
        music_composer: { type: "STRING" },
        sound_designer: { type: "STRING" }
      },
      required: ["ad_title", "brand", "product_category", "campaign_category", "runtime"]
    },
    executive_briefing: {
      type: "OBJECT",
      properties: {
        the_one_thing_that_matters: { type: "STRING" },
        gut_reaction: { type: "STRING" },
        scorecard: {
          type: "OBJECT",
          properties: {
            clarity_of_message: {
              type: "OBJECT",
              properties: {
                score: { type: "NUMBER" },
                justification: { type: "STRING" }
              },
              required: ["score", "justification"]
            },
            brand_distinctiveness: {
              type: "OBJECT",
              properties: {
                score: { type: "NUMBER" },
                justification: { type: "STRING" }
              },
              required: ["score", "justification"]
            },
            memorability_and_hook: {
              type: "OBJECT",
              properties: {
                score: { type: "NUMBER" },
                justification: { type: "STRING" }
              },
              required: ["score", "justification"]
            },
            emotional_resonance: {
              type: "OBJECT",
              properties: {
                score: { type: "NUMBER" },
                justification: { type: "STRING" }
              },
              required: ["score", "justification"]
            },
            business_objective_fit: {
              type: "OBJECT",
              properties: {
                score: { type: "NUMBER" },
                justification: { type: "STRING" }
              },
              required: ["score", "justification"]
            },
            call_to_action: {
              type: "OBJECT",
              properties: {
                score: { type: "NUMBER" },
                justification: { type: "STRING" }
              },
              required: ["score", "justification"]
            },
            overall_impact_score: {
              type: "OBJECT",
              properties: {
                score: { type: "NUMBER" },
                justification: { type: "STRING" }
              },
              required: ["score", "justification"]
            }
          },
          required: ["clarity_of_message", "brand_distinctiveness", "memorability_and_hook", "emotional_resonance", "business_objective_fit", "call_to_action", "overall_impact_score"]
        }
      },
      required: ["the_one_thing_that_matters", "gut_reaction", "scorecard"]
    },
    core_analysis: {
      type: "OBJECT",
      properties: {
        essay: { type: "STRING" }
      },
      required: ["essay"]
    },
    diagnosis: {
      type: "OBJECT",
      properties: {
        primary_pitfall: {
          type: "OBJECT",
          properties: {
            category: { type: "STRING" },
            description: { type: "STRING" },
            improvement_signal: { type: "STRING" }
          },
          required: ["category", "description", "improvement_signal"]
        },
        secondary_pitfall: {
          type: "OBJECT",
          properties: {
            category: { type: "STRING", nullable: true },
            description: { type: "STRING", nullable: true },
            improvement_signal: { type: "STRING", nullable: true }
          }
        }
      },
      required: ["primary_pitfall"]
    },
    strategic_deep_dive: {
      type: "OBJECT",
      properties: {
        brand_and_market_context: {
          type: "OBJECT",
          properties: {
            target_audience_evidence: { type: "STRING" },
            competitive_landscape_evidence: { type: "STRING" }
          },
          required: ["target_audience_evidence", "competitive_landscape_evidence"]
        },
        creative_and_cultural_context: {
          type: "OBJECT",
          properties: {
            creative_game_plan: { type: "STRING" },
            cultural_hook: { type: "STRING" }
          },
          required: ["creative_game_plan", "cultural_hook"]
        },
        brand_strategy: {
          type: "OBJECT",
          properties: {
            brand_position: { type: "STRING" },
            brand_archetype: { type: "STRING" },
            hypothetical_brief: { type: "STRING" }
          },
          required: ["brand_position", "brand_archetype", "hypothetical_brief"]
        }
      },
      required: ["brand_and_market_context", "creative_and_cultural_context", "brand_strategy"]
    },
    internal_signals: {
      type: "OBJECT",
      properties: {
        pattern_recognition: { type: "STRING" },
        prediction_factors: { type: "STRING" }
      },
      required: ["pattern_recognition", "prediction_factors"]
    },
    citations: {
      type: "ARRAY",
      items: {
        type: "OBJECT",
        properties: {
          title: { type: "STRING" },
          url: { type: "STRING" }
        },
        required: ["title", "url"]
      }
    }
  },
  required: ["metadata", "executive_briefing", "core_analysis", "diagnosis", "strategic_deep_dive", "internal_signals", "citations"]
};