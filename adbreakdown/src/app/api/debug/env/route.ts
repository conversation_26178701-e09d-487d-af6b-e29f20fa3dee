import { NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET() {
  try {
    const { userId } = await auth()
    
    console.log('🔍 DEBUG: Environment check', {
      userId: userId ? 'EXISTS' : 'NULL',
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV,
    })
    
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Create server Supabase client with service role permissions
    const supabase = createServerSupabaseClient()
    
    // Test user lookup
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()
    
    console.log('🔍 DEBUG: User lookup result', { user, userError })
    
    // Test database connection
    const { data: allUsers, error: allUsersError } = await supabase
      .from('users')
      .select('id, clerk_id, email, created_at')
      .order('created_at', { ascending: false })
      .limit(10)
    
    console.log('🔍 DEBUG: All users sample', { allUsers, allUsersError })
    
    return NextResponse.json({
      userId: userId ? 'EXISTS' : 'NULL',
      userFound: !!user,
      userError: userError?.message || null,
      totalUsers: allUsers?.length || 0,
      allUsers: allUsers?.map(u => ({
        id: u.id,
        clerk_id: u.clerk_id,
        email: u.email,
        created_at: u.created_at
      })) || [],
      environment: {
        NODE_ENV: process.env.NODE_ENV,
        VERCEL_ENV: process.env.VERCEL_ENV,
      },
      supabase: {
        url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'SET' : 'MISSING',
        anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'MISSING',
      }
    })
  } catch (error) {
    console.error('🔍 DEBUG: Error in env check', error)
    return NextResponse.json({ error: 'Debug check failed' }, { status: 500 })
  }
}