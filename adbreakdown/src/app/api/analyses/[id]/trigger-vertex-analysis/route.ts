import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { getMarketingAnalysisPrompt } from '@/lib/prompts/marketingAnalysisPrompt'
import { VertexAI } from '@google-cloud/vertexai'

interface RouteParams {
  params: Promise<{ 
    id: string
  }>
}

// POST /api/analyses/{id}/trigger-vertex-analysis
export async function POST(req: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    
    console.log('✅ SERVERLESS API: trigger-vertex-analysis called', {
      id,
      timestamp: new Date().toISOString(),
    })
    
    const { userId } = await auth()
    console.log('🔐 Clerk Authentication:', { userId: userId ? 'EXISTS' : 'NULL' })
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await req.json()
    const { youtubeUrl } = body

    if (!youtubeUrl) {
      return NextResponse.json({ error: 'YouTube URL is required' }, { status: 400 })
    }

    // Create server Supabase client with service role permissions (like working routes)
    const supabase = createServerSupabaseClient()
    
    // Get user from database (using EXACT same pattern as working routes)
    console.log('🔍 Vertex route: Looking for user with Clerk ID:', userId)
    console.log('🔍 Vertex route: Using server Supabase client with service role permissions')
    
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    console.log('🔍 Vertex route: User query result:', { user, userError })
    
    // If user not found, let's check what users DO exist
    if (!user) {
      console.log('🔍 Vertex route: User not found, checking what users exist...')
      const { data: allUsers, error: allUsersError } = await supabase
        .from('users')
        .select('id, clerk_id')
        .limit(5)
      
      console.log('🔍 Vertex route: Existing users sample:', { allUsers, allUsersError })
    }

    if (userError || !user) {
      console.log('🔧 Vertex route: User not found, attempting to create user (dev/prod sync)')
      
      // Auto-create user for dev/prod environment sync
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({ 
          clerk_id: userId,
          email: '<EMAIL>', // Default email for dev users
          first_name: 'Dev',
          last_name: 'User'
        })
        .select('id')
        .single()
      
      if (createError || !newUser) {
        console.error('❌ Vertex route: Failed to create user:', createError)
        return NextResponse.json({
          message: 'Analysis triggered successfully (testing mode - user creation failed)',
          analysis_id: id,
          status: 'completed',
          mock: true
        }, { status: 202 })
      }
      
      console.log('✅ Vertex route: Created new user for dev/prod sync:', { 
        clerkId: userId, 
        dbUserId: newUser.id 
      })
      
      user = newUser
    }

    console.log('✅ Found user:', { clerkId: userId, dbUserId: user.id })

    // Initialize Vertex AI with authentication
    const projectId = process.env.GOOGLE_PROJECT_ID
    const serviceAccountKey = process.env.GOOGLE_CLOUD_SERVICE_ACCOUNT_KEY
    
    console.log('🔧 Google Cloud Authentication Check:', {
      hasProjectId: !!projectId,
      hasServiceAccountKey: !!serviceAccountKey,
      serviceAccountKeyLength: serviceAccountKey?.length || 0,
      nodeEnv: process.env.NODE_ENV
    })
    
    if (!projectId) {
      console.error('❌ Missing GOOGLE_PROJECT_ID environment variable')
      return NextResponse.json(
        { error: 'Google Cloud Project ID not configured' },
        { status: 500 }
      )
    }

    let vertex_ai: VertexAI
    
    // Check if we have service account key for production
    if (serviceAccountKey) {
      try {
        console.log('🔑 Attempting to parse service account key...')
        let credentials;

        // The service account key might be a raw JSON string or a Base64 encoded string.
        // We'll try parsing it as raw JSON first.
        try {
          credentials = JSON.parse(serviceAccountKey);
          console.log('✅ Successfully parsed raw JSON from service account key.');
        } catch (rawParseError) {
          // If raw parsing fails, assume it's Base64 encoded, which is a common and robust way to store JSON in env vars.
          console.log('⚠️ Raw JSON parse failed, attempting Base64 decoding...');
          const decodedKey = Buffer.from(serviceAccountKey, 'base64').toString('utf-8');
          credentials = JSON.parse(decodedKey);
          console.log('✅ Successfully parsed Base64 decoded service account key.');
        }
        
        vertex_ai = new VertexAI({
          project: projectId,
          location: 'us-central1',
          googleAuthOptions: {
            credentials
          }
        })
        console.log('🔐 Successfully initialized Vertex AI with service account key authentication')
      } catch (error: any) {
        console.error('❌ Failed to parse service account key (tried raw and Base64):', {
          parseError: error.message,
          keyPreview: serviceAccountKey.substring(0, 100) + '...'
        })
        return NextResponse.json(
          { error: `Invalid service account credentials: ${error.message}` },
          { status: 500 }
        )
      }
    } else {
      console.log('⚠️ No service account key found, using default authentication (this will fail in production)')
      // Use default authentication (works in dev with gcloud auth)
      vertex_ai = new VertexAI({
        project: projectId,
        location: 'us-central1',
      })
      console.log('🔐 Initialized Vertex AI with default authentication (dev only)')
    }

    const model = vertex_ai.getGenerativeModel({ model: 'gemini-2.5-pro' });

    console.log('\n🌯 VERTEX AI ROUTE: Using local prompt for maximum speed...');
    const promptContent = getMarketingAnalysisPrompt(youtubeUrl)
    
    console.log('\n📊 VERTEX AI PROMPT SUMMARY:', {
      promptName: 'marketing_analysis_prompt',
      source: 'Local file (/lib/prompts/marketingAnalysisPrompt.ts)',
      length: promptContent.length,
      preview: promptContent.substring(0, 150) + '...',
      analysisMethod: 'Vertex AI'
    })
    
    const systemInstruction = {
      role: "system",
      parts: [{ text: promptContent }],
    };

    const generationConfig = {
      responseMimeType: 'application/json',
      temperature: 0.9,
      topP: 0.95,
    };

    const videoPart = {
      fileData: {
        mimeType: 'video/mp4',
        fileUri: youtubeUrl,
      },
    };

    // Vertex AI requires a text part alongside a file part in multimodal requests.
    const textPart = {
      text: `Please analyze the provided video advertisement based on the detailed system instructions.`
    };

    const result = await model.generateContent({
        contents: [{ role: "user", parts: [textPart, videoPart] }],
        systemInstruction,
        generationConfig
    });

    const response = result.response;
    
    // The response from the model is in the 'candidates' array.
    // We access the text from the first candidate's content parts.
    if (!response.candidates?.length || !response.candidates[0].content?.parts[0]?.text) {
      console.error("❌ Invalid response structure from Vertex AI:", JSON.stringify(response, null, 2));
      throw new Error("Received an invalid or empty text response from Vertex AI.");
    }
    
    const analysisText = response.candidates[0].content.parts[0].text;

    console.log('🔵 Raw response from Vertex AI:\n', analysisText);

    let analysisData;
    try {
      console.log('🔧 Original response length:', analysisText.length);
      console.log('🔧 Response starts with:', analysisText.substring(0, 100));
      console.log('🔧 Response ends with:', analysisText.substring(analysisText.length - 100));
      
      // First, try to parse as-is (most likely scenario)
      try {
        analysisData = JSON.parse(analysisText);
        console.log('✅ Successfully parsed JSON without cleaning');
      } catch (directParseError) {
        console.log('⚠️ Direct parse failed, trying with cleaning...');
        
        // If direct parse fails, try cleaning
        let cleanJsonText = analysisText
          .replace(/^```(json)?\s*/, '') // Remove opening code blocks
          .replace(/```\s*$/, '')        // Remove closing code blocks
          .trim();

        console.log('🔧 After cleaning length:', cleanJsonText.length);
        console.log('🔧 After cleaning starts with:', cleanJsonText.substring(0, 100));
        console.log('🔧 After cleaning ends with:', cleanJsonText.substring(cleanJsonText.length - 100));

        // Try to find JSON object boundaries more carefully
        const firstBrace = cleanJsonText.indexOf('{');
        const lastBrace = cleanJsonText.lastIndexOf('}');

        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
          cleanJsonText = cleanJsonText.substring(firstBrace, lastBrace + 1);
          console.log('🔧 After brace extraction length:', cleanJsonText.length);
          console.log('🔧 After brace extraction starts with:', cleanJsonText.substring(0, 100));
          console.log('🔧 After brace extraction ends with:', cleanJsonText.substring(cleanJsonText.length - 100));
        }

        analysisData = JSON.parse(cleanJsonText);
        console.log('✅ Successfully parsed JSON after cleaning');
      }
    } catch (parseError: any) {
      console.error('❌ Failed to parse JSON from Vertex AI response:', {
        parseErrorMessage: parseError.message,
        rawResponseLength: analysisText.length,
        rawResponsePreview: analysisText.substring(0, 500),
        rawResponseEnd: analysisText.substring(analysisText.length - 500),
      });
      return NextResponse.json(
        {
          error: `Failed to parse analysis data from Vertex AI response: ${parseError.message}`,
        },
        { status: 500 }
      );
    }

    // ✅ CRITICAL: UUID/Slug resolution pattern to prevent database errors
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    console.log('🔧 ID resolution:', { id, isUUID, fieldToMatch: isUUID ? 'id' : 'slug' })
    
    // First get the analysis to ensure it exists and verify ownership
    const { data: analysis, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('id, user_id, slug')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)  // ← Verify ownership
      .single()

    if (fetchError || !analysis) {
      console.error('❌ Failed to fetch analysis for update:', fetchError)
      return NextResponse.json(
        { error: `Analysis not found or access denied: ${fetchError?.message || 'Record not found or user does not own this analysis'}` },
        { status: 404 }
      )
    }

    console.log('✅ Found analysis for update:', { analysisId: analysis.id, slug: analysis.slug })

    // Now update using the actual UUID
    const { data: updatedData, error: updateError } = await supabase
      .from('ad_analyses')
      .update({
        marketing_analysis: analysisData,
        status: 'completed',
        analysis_completed_at: new Date().toISOString(),
      })
      .eq('id', analysis.id)  // ← Use the actual UUID from the fetched analysis
      .select()
      .single();

    if (updateError || !updatedData) {
      console.error('❌ Failed to save analysis to Supabase:', updateError);
      // If the update fails, we must set the status to 'failed' to prevent client-side loops
      await supabase
        .from('ad_analyses')
        .update({ status: 'failed' })
        .eq('id', analysis.id);  // ← Use the actual UUID from the fetched analysis
      return NextResponse.json(
        { error: `Failed to update database: ${updateError?.message || 'Record not found or update failed.'}` },
        { status: 500 }
      );
    }

    console.log('✅ Successfully saved analysis to database for ID:', updatedData.id);

    return NextResponse.json(
      { 
        message: 'Vertex analysis completed and saved successfully',
        analysis_id: analysis.id,  // ← Use the actual UUID
        slug: analysis.slug,
        status: 'completed',
        data: analysisData
      },
      { status: 200 }
    )
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    console.error('❌ Error in trigger-vertex-analysis:', {
      errorMessage,
      errorObject: error,
    });
    
    return NextResponse.json(
      { error: `An internal server error occurred: ${errorMessage}` },
      { status: 500 }
    )
  }
}