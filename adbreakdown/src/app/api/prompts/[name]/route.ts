import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import PromptManager from '@/lib/prompts/promptManager'

interface RouteParams {
  params: Promise<{
    name: string
  }>
}

// GET /api/prompts/[name] - Get prompt content for client-side use
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { name } = await params
    const { searchParams } = new URL(req.url)
    
    // Parse template variables from query params
    const templateVars: Record<string, string> = {}
    for (const [key, value] of searchParams.entries()) {
      if (key !== 'name') {
        templateVars[key] = value
      }
    }
    
    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('\n🌐 CLIENT API: /api/prompts/[name] called', {
        name,
        templateVars,
        userId: userId ? 'EXISTS' : 'NULL'
      })
    }
    
    const promptContent = await PromptManager.getPrompt(name, templateVars)
    
    if (process.env.NODE_ENV === 'development') {
      console.log('✅ CLIENT API: Successfully retrieved prompt', {
        name,
        contentLength: promptContent.length,
        templateVarsCount: Object.keys(templateVars).length
      })
    }
    
    return NextResponse.json({ 
      content: promptContent,
      name: name,
      templateVars: templateVars,
      source: 'api'
    })
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('❌ CLIENT API: Error fetching prompt', {
        error: error instanceof Error ? error.message : String(error)
      })
    }
    console.error('Error fetching prompt:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}