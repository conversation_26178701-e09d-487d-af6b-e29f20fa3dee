import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import PromptManager from '@/lib/prompts/promptManager'

// Admin email addresses (move to environment variable in production)
const ADMIN_EMAILS = [
  '<EMAIL>'
]

// Check if user is admin
async function isUserAdmin(userId: string): Promise<boolean> {
  // In development, allow any authenticated user
  if (process.env.NODE_ENV === 'development') {
    return true
  }
  
  // In production, check against admin email list
  const supabase = createServerSupabaseClient()
  const { data: user } = await supabase
    .from('users')
    .select('email')
    .eq('clerk_id', userId)
    .single()
    
  return user?.email && ADMIN_EMAILS.includes(user.email)
}

// GET /api/admin/prompts - List all prompts
export async function GET() {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Check admin access
    const isAdmin = await isUserAdmin(userId)
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const supabase = createServerSupabaseClient()
    const { data: prompts, error } = await supabase
      .from('prompts')
      .select('*')
      .order('updated_at', { ascending: false })

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    // Format prompts for better readability
    const formattedPrompts = prompts.map(prompt => ({
      ...prompt,
      content_preview: prompt.content.substring(0, 200) + '...',
      content_lines: prompt.content.split('\n').length,
      last_updated: new Date(prompt.updated_at).toLocaleString()
    }))

    return NextResponse.json({ prompts: formattedPrompts })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/admin/prompts - Create or update prompt
export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Check admin access
    const isAdmin = await isUserAdmin(userId)
    if (!isAdmin) {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    const { name, content, description } = await req.json()

    if (!name || !content) {
      return NextResponse.json({ error: 'Name and content are required' }, { status: 400 })
    }

    const success = await PromptManager.updatePrompt(name, content, description)
    
    if (!success) {
      return NextResponse.json({ error: 'Failed to update prompt' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Prompt updated successfully' })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}